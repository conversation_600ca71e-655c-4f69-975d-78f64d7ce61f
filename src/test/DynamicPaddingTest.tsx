/**
 * 动态Padding测试组件
 * 测试便签内容区域的动态padding功能
 * 验证滚动条出现时padding的自动调整
 */

import React, { useState } from "react";
import { Card, Button, Space, Typography, Alert } from "antd";

const { Title, Paragraph, Text } = Typography;

/**
 * 动态Padding测试组件
 */
const DynamicPaddingTest: React.FC = () => {
  const [testResults, setTestResults] = useState<string[]>([]);

  // 添加测试结果
  const addTestResult = (result: string) => {
    setTestResults((prev) => [
      ...prev,
      `${new Date().toLocaleTimeString()}: ${result}`,
    ]);
  };

  // 清除测试结果
  const clearResults = () => {
    setTestResults([]);
  };

  // 测试说明
  const testInstructions = [
    "1. 创建一个新便签",
    "2. 输入少量内容，观察右侧padding为16px",
    "3. 继续输入内容直到出现滚动条",
    "4. 观察右侧padding自动调整为1px",
    "5. 调整便签大小，观察padding的动态变化",
    "6. 缩放画布，观察padding在不同缩放级别下的表现",
  ];

  return (
    <div style={{ padding: "20px", maxWidth: "1200px", margin: "0 auto" }}>
      <Title level={2}>🎯 动态Padding测试</Title>

      <Alert
        message="测试说明"
        description={
          <div>
            <Paragraph>此测试验证便签内容区域的动态padding功能：</Paragraph>
            <ul>
              {testInstructions.map((instruction, index) => (
                <li key={index}>{instruction}</li>
              ))}
            </ul>
          </div>
        }
        type="info"
        style={{ marginBottom: "20px" }}
      />

      <div style={{ display: "flex", gap: "20px" }}>
        {/* 左侧：测试控制 */}
        <Card title="测试控制" style={{ flex: 1 }}>
          <Space direction="vertical" style={{ width: "100%" }}>
            <Button
              type="primary"
              onClick={() => addTestResult("开始测试 - 请按照说明操作便签")}
            >
              开始测试
            </Button>

            <Button
              onClick={() =>
                addTestResult(
                  "检查CSS规则 - 查看.sticky-note-content的padding-right值"
                )
              }
            >
              检查CSS规则
            </Button>

            <Button
              onClick={() =>
                addTestResult(
                  "检查data-scrollable属性 - 查看ProseMirror元素的data-scrollable属性"
                )
              }
            >
              检查滚动条属性
            </Button>

            <Button onClick={clearResults}>清除结果</Button>
          </Space>
        </Card>

        {/* 右侧：测试结果 */}
        <Card title="测试结果" style={{ flex: 2 }}>
          <div
            style={{
              height: "400px",
              overflow: "auto",
              border: "1px solid #d9d9d9",
              borderRadius: "6px",
              padding: "10px",
              backgroundColor: "#fafafa",
            }}
          >
            {testResults.length === 0 ? (
              <Text type="secondary">暂无测试结果...</Text>
            ) : (
              testResults.map((result, index) => (
                <div key={index} style={{ marginBottom: "8px" }}>
                  <Text code>{result}</Text>
                </div>
              ))
            )}
          </div>
        </Card>
      </div>

      {/* 技术说明 */}
      <Card title="技术实现说明" style={{ marginTop: "20px" }}>
        <Space direction="vertical" style={{ width: "100%" }}>
          <Paragraph>
            <Text strong>CSS选择器实现：</Text>
          </Paragraph>
          <pre
            style={{
              backgroundColor: "#f6f8fa",
              padding: "10px",
              borderRadius: "4px",
            }}
          >
            {`/* 默认padding */
.sticky-note-content {
  padding-right: 16px;
}

/* 有滚动条时的padding */
.sticky-note-content:has(.ProseMirror[data-scrollable="true"]) {
  padding-right: 1px;
}

/* 备用方案（兼容性） */
.sticky-note-content.has-scrollbar {
  padding-right: 1px;
}`}
          </pre>

          <Paragraph>
            <Text strong>JavaScript检测逻辑：</Text>
          </Paragraph>
          <ul>
            <li>使用ResizeObserver监听容器大小变化</li>
            <li>使用MutationObserver监听内容变化</li>
            <li>通过scrollHeight &gt; clientHeight检测滚动条</li>
            <li>动态设置data-scrollable属性</li>
            <li>使用防抖优化性能</li>
          </ul>
        </Space>
      </Card>
    </div>
  );
};

export default DynamicPaddingTest;
