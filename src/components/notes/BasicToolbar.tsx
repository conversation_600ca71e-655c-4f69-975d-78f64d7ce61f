/**
 * 基础工具栏组件
 * 严格按照 Tiptap 官方文档实现，只包含基本的格式化功能
 * 优化UI设计，与便签风格匹配
 */

import React from "react";

/**
 * 工具栏属性接口
 */
interface BasicToolbarProps {
  /** 编辑器实例 */
  editor: any;
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

/**
 * 创建按钮样式的辅助函数
 */
const createButtonStyle = (isActive: boolean) => ({
  padding: "3px 5px", // 增加按钮内边距，让按钮稍微大一点
  border: "none",
  borderRadius: "3px", // 稍微增大圆角
  backgroundColor: isActive
    ? "rgba(59, 130, 246, 0.15)"
    : "rgba(0, 0, 0, 0.02)", // 非激活状态也有轻微背景
  color: isActive ? "#3b82f6" : "#4b5563", // 调整颜色对比度
  fontSize: "11px", // 增大字体
  fontWeight: "500",
  cursor: "pointer",
  transition: "all 0.15s ease", // 稍微慢一点的过渡
  minWidth: "22px", // 增大最小宽度
  height: "22px", // 增大高度
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
});

/**
 * 基础工具栏组件
 * 提供常用的格式化按钮，小巧精致的设计
 */
const BasicToolbar: React.FC<BasicToolbarProps> = ({
  editor,
  className = "",
  style = {},
}) => {
  // 如果编辑器未初始化，不显示工具栏
  if (!editor) {
    return null;
  }

  // 按钮悬停效果处理
  const handleMouseEnter = (
    e: React.MouseEvent<HTMLButtonElement>,
    isActive: boolean
  ) => {
    if (!isActive) {
      e.currentTarget.style.backgroundColor = "rgba(0, 0, 0, 0.05)";
    }
  };

  const handleMouseLeave = (
    e: React.MouseEvent<HTMLButtonElement>,
    isActive: boolean
  ) => {
    if (!isActive) {
      e.currentTarget.style.backgroundColor = "transparent";
    }
  };

  return (
    <div
      className={`basic-toolbar ${className}`}
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "flex-start", // 工具从左到右分布
        gap: "4px", // 增加按钮间距，让工具更好分布
        padding: "4px 0", // 减少左右padding，增加上下padding
        backgroundColor: "transparent", // 透明背景
        border: "none", // 移除边框
        boxShadow: "none", // 移除阴影
        fontSize: "11px", // 稍微增大字体
        fontFamily: "inherit",
        marginBottom: 0,
        flexWrap: "nowrap", // 不换行，保持一行显示
        width: "100%", // 占满容器宽度
        ...style, // 合并传入的自定义样式
      }}
    >
      {/* 基础格式化按钮 */}
      <div style={{ display: "flex", alignItems: "center", gap: "2px" }}>
        <button
          onClick={() => editor.chain().focus().toggleBold().run()}
          disabled={!editor.can().chain().focus().toggleBold().run()}
          title="粗体 (Ctrl+B)"
          style={createButtonStyle(editor.isActive("bold"))}
          onMouseEnter={(e) => handleMouseEnter(e, editor.isActive("bold"))}
          onMouseLeave={(e) => handleMouseLeave(e, editor.isActive("bold"))}
        >
          <strong>B</strong>
        </button>

        <button
          onClick={() => editor.chain().focus().toggleItalic().run()}
          disabled={!editor.can().chain().focus().toggleItalic().run()}
          title="斜体 (Ctrl+I)"
          style={createButtonStyle(editor.isActive("italic"))}
          onMouseEnter={(e) => handleMouseEnter(e, editor.isActive("italic"))}
          onMouseLeave={(e) => handleMouseLeave(e, editor.isActive("italic"))}
        >
          <em>I</em>
        </button>

        <button
          onClick={() => editor.chain().focus().toggleStrike().run()}
          disabled={!editor.can().chain().focus().toggleStrike().run()}
          title="删除线"
          style={createButtonStyle(editor.isActive("strike"))}
          onMouseEnter={(e) => handleMouseEnter(e, editor.isActive("strike"))}
          onMouseLeave={(e) => handleMouseLeave(e, editor.isActive("strike"))}
        >
          <s>S</s>
        </button>

        <button
          onClick={() => editor.chain().focus().toggleCode().run()}
          disabled={!editor.can().chain().focus().toggleCode().run()}
          title="行内代码"
          style={{
            ...createButtonStyle(editor.isActive("code")),
            fontSize: "10px", // 与新的按钮尺寸协调
            minWidth: "26px", // 稍微增大宽度
          }}
          onMouseEnter={(e) => handleMouseEnter(e, editor.isActive("code"))}
          onMouseLeave={(e) => handleMouseLeave(e, editor.isActive("code"))}
        >
          &lt;/&gt;
        </button>
      </div>

      {/* 分隔符 */}
      <div
        style={{
          width: "1px",
          height: "16px", // 增大分隔符高度，与按钮高度协调
          backgroundColor: "rgba(0, 0, 0, 0.1)", // 稍微深一点的颜色，在透明背景下更明显
          margin: "0 6px", // 增大边距，让分组更明显
        }}
      ></div>

      {/* 列表按钮 */}
      <div style={{ display: "flex", alignItems: "center", gap: "2px" }}>
        <button
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          title="无序列表"
          style={{
            ...createButtonStyle(editor.isActive("bulletList")),
            fontSize: "11px", // 稍微减小字体
            fontWeight: "500",
          }}
          onMouseEnter={(e) =>
            handleMouseEnter(e, editor.isActive("bulletList"))
          }
          onMouseLeave={(e) =>
            handleMouseLeave(e, editor.isActive("bulletList"))
          }
        >
          •
        </button>

        <button
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          title="有序列表"
          style={{
            ...createButtonStyle(editor.isActive("orderedList")),
            fontSize: "10px", // 与新的按钮尺寸协调
            fontWeight: "500",
          }}
          onMouseEnter={(e) =>
            handleMouseEnter(e, editor.isActive("orderedList"))
          }
          onMouseLeave={(e) =>
            handleMouseLeave(e, editor.isActive("orderedList"))
          }
        >
          1.
        </button>

        <button
          onClick={() => editor.chain().focus().toggleTaskList().run()}
          title="任务列表"
          style={{
            ...createButtonStyle(editor.isActive("taskList")),
            fontSize: "11px", // 稍微减小字体
            fontWeight: "500",
          }}
          onMouseEnter={(e) => handleMouseEnter(e, editor.isActive("taskList"))}
          onMouseLeave={(e) => handleMouseLeave(e, editor.isActive("taskList"))}
        >
          ☐
        </button>
      </div>

      {/* 分隔符 */}
      <div
        style={{
          width: "1px",
          height: "16px", // 增大分隔符高度，与按钮高度协调
          backgroundColor: "rgba(0, 0, 0, 0.1)", // 稍微深一点的颜色，在透明背景下更明显
          margin: "0 6px", // 增大边距，让分组更明显
        }}
      ></div>

      {/* 表格按钮 */}
      <div style={{ display: "flex", alignItems: "center", gap: "2px" }}>
        <button
          onClick={() => {
            if (editor.isActive("table")) {
              editor.chain().focus().deleteTable().run();
            } else {
              editor
                .chain()
                .focus()
                .insertTable({
                  rows: 3,
                  cols: 3,
                  withHeaderRow: true,
                })
                .run();
            }
          }}
          title={editor.isActive("table") ? "删除表格" : "插入表格 (3x3)"}
          style={{
            ...createButtonStyle(editor.isActive("table")),
            fontSize: "11px", // 稍微减小字体
            fontWeight: "500",
          }}
          onMouseEnter={(e) => handleMouseEnter(e, editor.isActive("table"))}
          onMouseLeave={(e) => handleMouseLeave(e, editor.isActive("table"))}
        >
          ⊞
        </button>

        {editor.isActive("table") && (
          <>
            <button
              onClick={() => editor.chain().focus().addColumnAfter().run()}
              disabled={!editor.can().addColumnAfter()}
              title="添加列"
              style={{
                ...createButtonStyle(false),
                fontSize: "10px", // 与新的按钮尺寸协调
                fontWeight: "500",
              }}
              onMouseEnter={(e) => handleMouseEnter(e, false)}
              onMouseLeave={(e) => handleMouseLeave(e, false)}
            >
              +│
            </button>

            <button
              onClick={() => editor.chain().focus().addRowAfter().run()}
              disabled={!editor.can().addRowAfter()}
              title="添加行"
              style={{
                ...createButtonStyle(false),
                fontSize: "10px", // 与新的按钮尺寸协调
                fontWeight: "500",
              }}
              onMouseEnter={(e) => handleMouseEnter(e, false)}
              onMouseLeave={(e) => handleMouseLeave(e, false)}
            >
              +─
            </button>
          </>
        )}
      </div>

      {/* 标题功能已移除，用户可以通过 Markdown 语法使用：# 标题1  ## 标题2  ### 标题3 */}
    </div>
  );
};

export default BasicToolbar;
