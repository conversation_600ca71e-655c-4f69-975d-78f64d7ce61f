/**
 * 基础工具栏组件
 * 严格按照 Tiptap 官方文档实现，只包含基本的格式化功能
 * 优化UI设计，与便签风格匹配
 */

import React from "react";

/**
 * 工具栏属性接口
 */
interface BasicToolbarProps {
  /** 编辑器实例 */
  editor: any;
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
}

/**
 * 创建按钮样式的辅助函数 - 标准化设计
 */
const createButtonStyle = (isActive: boolean) => ({
  padding: "4px 6px", // 标准按钮内边距
  border: "1px solid transparent", // 透明边框，激活时显示
  borderRadius: "4px", // 标准圆角
  backgroundColor: isActive
    ? "rgba(59, 130, 246, 0.12)" // 激活状态：蓝色背景
    : "rgba(255, 255, 255, 0.8)", // 非激活状态：半透明白色背景
  borderColor: isActive
    ? "rgba(59, 130, 246, 0.2)" // 激活状态：蓝色边框
    : "rgba(0, 0, 0, 0.08)", // 非激活状态：浅灰边框
  color: isActive ? "#2563eb" : "#374151", // 标准文字颜色
  fontSize: "12px", // 标准字体大小
  fontWeight: isActive ? "600" : "500", // 激活时加粗
  cursor: "pointer",
  transition: "all 0.2s ease", // 标准过渡时间
  minWidth: "24px", // 标准最小宽度
  height: "24px", // 标准高度
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  boxShadow: isActive
    ? "0 1px 2px rgba(59, 130, 246, 0.1)" // 激活状态：轻微阴影
    : "0 1px 1px rgba(0, 0, 0, 0.05)", // 非激活状态：极轻阴影
});

/**
 * 基础工具栏组件
 * 提供常用的格式化按钮，小巧精致的设计
 */
const BasicToolbar: React.FC<BasicToolbarProps> = ({
  editor,
  className = "",
  style = {},
}) => {
  // 如果编辑器未初始化，不显示工具栏
  if (!editor) {
    return null;
  }

  // 按钮悬停效果处理 - 标准化交互
  const handleMouseEnter = (
    e: React.MouseEvent<HTMLButtonElement>,
    isActive: boolean
  ) => {
    if (!isActive) {
      e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.08)";
      e.currentTarget.style.borderColor = "rgba(59, 130, 246, 0.15)";
      e.currentTarget.style.transform = "translateY(-1px)"; // 轻微上移效果
    } else {
      e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.18)";
      e.currentTarget.style.transform = "translateY(-1px)";
    }
  };

  const handleMouseLeave = (
    e: React.MouseEvent<HTMLButtonElement>,
    isActive: boolean
  ) => {
    if (!isActive) {
      e.currentTarget.style.backgroundColor = "rgba(255, 255, 255, 0.8)";
      e.currentTarget.style.borderColor = "rgba(0, 0, 0, 0.08)";
      e.currentTarget.style.transform = "translateY(0)";
    } else {
      e.currentTarget.style.backgroundColor = "rgba(59, 130, 246, 0.12)";
      e.currentTarget.style.transform = "translateY(0)";
    }
  };

  return (
    <div
      className={`basic-toolbar ${className}`}
      style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center", // 工具栏居中显示，更美观
        gap: "3px", // 标准按钮间距
        padding: "0", // 移除内边距，由外层容器控制
        backgroundColor: "transparent", // 透明背景，由外层容器提供背景
        border: "none", // 无边框
        boxShadow: "none", // 无阴影
        fontSize: "12px", // 标准字体大小
        fontFamily: "inherit",
        marginBottom: 0,
        flexWrap: "nowrap", // 不换行，保持一行显示
        width: "auto", // 自适应宽度
        maxWidth: "100%", // 不超过容器宽度
        ...style, // 合并传入的自定义样式
      }}
    >
      {/* 基础格式化按钮组 */}
      <div style={{ display: "flex", alignItems: "center", gap: "2px" }}>
        <button
          onClick={() => editor.chain().focus().toggleBold().run()}
          disabled={!editor.can().chain().focus().toggleBold().run()}
          title="粗体 (Ctrl+B)"
          style={createButtonStyle(editor.isActive("bold"))}
          onMouseEnter={(e) => handleMouseEnter(e, editor.isActive("bold"))}
          onMouseLeave={(e) => handleMouseLeave(e, editor.isActive("bold"))}
        >
          <strong>B</strong>
        </button>

        <button
          onClick={() => editor.chain().focus().toggleItalic().run()}
          disabled={!editor.can().chain().focus().toggleItalic().run()}
          title="斜体 (Ctrl+I)"
          style={createButtonStyle(editor.isActive("italic"))}
          onMouseEnter={(e) => handleMouseEnter(e, editor.isActive("italic"))}
          onMouseLeave={(e) => handleMouseLeave(e, editor.isActive("italic"))}
        >
          <em>I</em>
        </button>

        <button
          onClick={() => editor.chain().focus().toggleStrike().run()}
          disabled={!editor.can().chain().focus().toggleStrike().run()}
          title="删除线"
          style={createButtonStyle(editor.isActive("strike"))}
          onMouseEnter={(e) => handleMouseEnter(e, editor.isActive("strike"))}
          onMouseLeave={(e) => handleMouseLeave(e, editor.isActive("strike"))}
        >
          <s>S</s>
        </button>

        <button
          onClick={() => editor.chain().focus().toggleCode().run()}
          disabled={!editor.can().chain().focus().toggleCode().run()}
          title="行内代码 (Ctrl+`)"
          style={{
            ...createButtonStyle(editor.isActive("code")),
            fontSize: "10px", // 代码符号使用较小字体
            minWidth: "26px", // 稍微增大宽度以容纳符号
          }}
          onMouseEnter={(e) => handleMouseEnter(e, editor.isActive("code"))}
          onMouseLeave={(e) => handleMouseLeave(e, editor.isActive("code"))}
        >
          &lt;/&gt;
        </button>
      </div>

      {/* 分隔符 */}
      <div
        style={{
          width: "1px",
          height: "18px", // 与按钮高度协调
          backgroundColor: "rgba(0, 0, 0, 0.12)", // 标准分隔符颜色
          margin: "0 4px", // 标准边距
          flexShrink: 0, // 不允许收缩
        }}
      ></div>

      {/* 列表按钮组 */}
      <div style={{ display: "flex", alignItems: "center", gap: "2px" }}>
        <button
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          disabled={!editor.can().chain().focus().toggleBulletList().run()}
          title="无序列表 (Ctrl+Shift+8)"
          style={createButtonStyle(editor.isActive("bulletList"))}
          onMouseEnter={(e) =>
            handleMouseEnter(e, editor.isActive("bulletList"))
          }
          onMouseLeave={(e) =>
            handleMouseLeave(e, editor.isActive("bulletList"))
          }
        >
          •
        </button>

        <button
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          disabled={!editor.can().chain().focus().toggleOrderedList().run()}
          title="有序列表 (Ctrl+Shift+7)"
          style={{
            ...createButtonStyle(editor.isActive("orderedList")),
            fontSize: "10px", // 数字使用较小字体
          }}
          onMouseEnter={(e) =>
            handleMouseEnter(e, editor.isActive("orderedList"))
          }
          onMouseLeave={(e) =>
            handleMouseLeave(e, editor.isActive("orderedList"))
          }
        >
          1.
        </button>

        <button
          onClick={() => editor.chain().focus().toggleTaskList().run()}
          disabled={!editor.can().chain().focus().toggleTaskList().run()}
          title="任务列表 (Ctrl+Shift+9)"
          style={createButtonStyle(editor.isActive("taskList"))}
          onMouseEnter={(e) => handleMouseEnter(e, editor.isActive("taskList"))}
          onMouseLeave={(e) => handleMouseLeave(e, editor.isActive("taskList"))}
        >
          ☐
        </button>
      </div>

      {/* 分隔符 */}
      <div
        style={{
          width: "1px",
          height: "18px", // 与按钮高度协调
          backgroundColor: "rgba(0, 0, 0, 0.12)", // 标准分隔符颜色
          margin: "0 4px", // 标准边距
          flexShrink: 0, // 不允许收缩
        }}
      ></div>

      {/* 表格按钮组 */}
      <div style={{ display: "flex", alignItems: "center", gap: "2px" }}>
        <button
          onClick={() => {
            if (editor.isActive("table")) {
              // 标准的删除表格命令
              editor.chain().focus().deleteTable().run();
            } else {
              // 标准的插入表格命令
              editor
                .chain()
                .focus()
                .insertTable({
                  rows: 3,
                  cols: 3,
                  withHeaderRow: true,
                })
                .run();
            }
          }}
          title={editor.isActive("table") ? "删除表格" : "插入表格 (3x3)"}
          style={createButtonStyle(editor.isActive("table"))}
          onMouseEnter={(e) => handleMouseEnter(e, editor.isActive("table"))}
          onMouseLeave={(e) => handleMouseLeave(e, editor.isActive("table"))}
        >
          ⊞
        </button>

        {editor.isActive("table") && (
          <>
            <button
              onClick={() => editor.chain().focus().addColumnAfter().run()}
              disabled={!editor.can().addColumnAfter()}
              title="添加列"
              style={{
                ...createButtonStyle(false),
                fontSize: "10px", // 符号使用较小字体
              }}
              onMouseEnter={(e) => handleMouseEnter(e, false)}
              onMouseLeave={(e) => handleMouseLeave(e, false)}
            >
              +│
            </button>

            <button
              onClick={() => editor.chain().focus().addRowAfter().run()}
              disabled={!editor.can().addRowAfter()}
              title="添加行"
              style={{
                ...createButtonStyle(false),
                fontSize: "10px", // 符号使用较小字体
              }}
              onMouseEnter={(e) => handleMouseEnter(e, false)}
              onMouseLeave={(e) => handleMouseLeave(e, false)}
            >
              +─
            </button>
          </>
        )}
      </div>

      {/* 标题功能已移除，用户可以通过 Markdown 语法使用：# 标题1  ## 标题2  ### 标题3 */}
    </div>
  );
};

export default BasicToolbar;
