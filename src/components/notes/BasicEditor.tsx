/**
 * 基础 Tiptap 编辑器组件
 * 严格按照官方文档实现，只包含最基本的功能
 * 参考：https://tiptap.dev/docs/editor/getting-started/install/react
 */

import React, { useCallback, useEffect, useRef } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import { Table } from "@tiptap/extension-table";
import { TableRow } from "@tiptap/extension-table-row";
import { TableCell } from "@tiptap/extension-table-cell";
import { TableHeader } from "@tiptap/extension-table-header";
import { TaskList } from "@tiptap/extension-list";
import { TaskItem } from "@tiptap/extension-list";
// BasicToolbar 现在由父组件管理

// 防抖函数，用于优化滚动条检测性能
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout;
  return function executedFunction(...args: any[]) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * 基础编辑器属性接口
 */
interface BasicEditorProps {
  /** 编辑器内容 */
  content?: string;
  /** 内容变化回调 */
  onChange?: (content: string) => void;
  /** 编辑器准备就绪回调 */
  onEditorReady?: (editor: any) => void;
  /** 滚动条状态变化回调 */
  onScrollbarChange?: (hasScrollbar: boolean) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否自动聚焦 */
  autoFocus?: boolean;
  /** 是否可编辑 */
  editable?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 点击事件 */
  onClick?: (e: React.MouseEvent) => void;
  /** 鼠标按下事件 */
  onMouseDown?: (e: React.MouseEvent) => void;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 标题属性 */
  title?: string;
  // showToolbar 已移除，工具栏现在由父组件管理
}

/**
 * 基础编辑器组件
 * 使用 Tiptap 官方推荐的最简单配置
 */
const BasicEditor: React.FC<BasicEditorProps> = ({
  content = "",
  onChange,
  onEditorReady,
  onScrollbarChange,
  placeholder = "开始输入...",
  autoFocus = false,
  editable = true,
  className = "",
  onClick,
  onMouseDown,
  style,
  title,
}) => {
  // 编辑器容器引用，用于滚动条检测
  const editorContentRef = useRef<HTMLDivElement>(null);
  // ProseMirror元素引用，用于设置data-scrollable属性
  const proseMirrorRef = useRef<HTMLElement | null>(null);
  // 使用官方推荐的 useEditor hook
  const editor = useEditor({
    // 使用 StarterKit 扩展，包含最常用的基础功能
    extensions: [
      StarterKit.configure({
        // 可以在这里配置 StarterKit 的选项
        // 暂时使用默认配置
      }),
      // 添加 Placeholder 扩展，按照官方文档配置
      Placeholder.configure({
        placeholder: placeholder,
        emptyEditorClass: "is-editor-empty",
        emptyNodeClass: "is-empty",
        showOnlyWhenEditable: true,
        showOnlyCurrent: true,
      }),
      // 添加表格扩展，按照官方文档配置
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      // 添加任务列表扩展，按照官方文档配置
      TaskList.configure({
        HTMLAttributes: {
          class: "task-list",
        },
      }),
      // 添加任务项扩展，任务列表需要此扩展
      TaskItem.configure({
        HTMLAttributes: {
          class: "task-item",
        },
      }),
    ],
    // 设置初始内容
    content: content,
    // 设置是否可编辑
    editable: editable,
    // 设置是否自动聚焦
    autofocus: autoFocus,
    // 内容更新回调
    onUpdate: ({ editor }) => {
      if (onChange) {
        // 获取 HTML 格式的内容
        const html = editor.getHTML();
        onChange(html);
      }
    },
    // 编辑器创建完成回调
    onCreate: ({ editor }) => {
      if (onEditorReady) {
        onEditorReady(editor);
      }
      // 设置ProseMirror元素引用
      setTimeout(() => {
        const proseMirrorElement = editor.view?.dom;
        if (proseMirrorElement) {
          proseMirrorRef.current = proseMirrorElement;
          // 初始化滚动条检测
          checkScrollbar();
        }
      }, 0);
    },
  });

  /**
   * 检测编辑器是否有滚动条，并设置相应的data-scrollable属性
   * 这个函数会在内容变化、容器大小变化时被调用
   */
  const checkScrollbar = useCallback(() => {
    if (!editorContentRef.current || !proseMirrorRef.current) {
      return;
    }

    const container = editorContentRef.current;
    const proseMirrorElement = proseMirrorRef.current;

    // 检测是否有垂直滚动条
    // scrollHeight > clientHeight 表示内容超出容器高度，需要滚动
    const hasVerticalScrollbar =
      container.scrollHeight > container.clientHeight;

    // 设置data-scrollable属性
    if (hasVerticalScrollbar) {
      proseMirrorElement.setAttribute("data-scrollable", "true");
    } else {
      proseMirrorElement.setAttribute("data-scrollable", "false");
    }

    // 通知父组件滚动条状态变化
    if (onScrollbarChange) {
      onScrollbarChange(hasVerticalScrollbar);
    }

    // 开发环境下的调试信息
    if (process.env.NODE_ENV === "development") {
      console.log("🔍 滚动条检测:", {
        hasScrollbar: hasVerticalScrollbar,
        scrollHeight: container.scrollHeight,
        clientHeight: container.clientHeight,
        element: proseMirrorElement,
      });
    }
  }, [onScrollbarChange]);

  // 创建防抖版本的滚动条检测函数，避免频繁调用
  const debouncedCheckScrollbar = useCallback(
    debounce(checkScrollbar, 100), // 100ms防抖
    [checkScrollbar]
  );

  // 当外部 content 发生变化时，更新编辑器内容
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content, { emitUpdate: false }); // 修正为对象类型
      // 内容变化后检测滚动条
      setTimeout(checkScrollbar, 0);
    }
  }, [content, editor, checkScrollbar]);

  // 当 editable 属性变化时，更新编辑器的可编辑状态
  useEffect(() => {
    if (editor) {
      editor.setEditable(editable);
    }
  }, [editable, editor]);

  // 监听编辑器内容变化和容器大小变化，动态检测滚动条
  useEffect(() => {
    if (!editor || !editorContentRef.current) {
      return;
    }

    const container = editorContentRef.current;

    // 创建ResizeObserver来监听容器大小变化
    const resizeObserver = new ResizeObserver(() => {
      // 使用防抖版本，避免频繁调用
      debouncedCheckScrollbar();
    });

    // 创建MutationObserver来监听内容变化
    const mutationObserver = new MutationObserver(() => {
      // 使用防抖版本，避免频繁调用
      debouncedCheckScrollbar();
    });

    // 开始观察
    resizeObserver.observe(container);

    // 观察ProseMirror元素的子树变化
    const proseMirrorElement = editor.view?.dom;
    if (proseMirrorElement) {
      mutationObserver.observe(proseMirrorElement, {
        childList: true,
        subtree: true,
        characterData: true,
      });
    }

    // 监听编辑器的更新事件
    const handleUpdate = () => {
      // 使用防抖版本，避免频繁调用
      debouncedCheckScrollbar();
    };

    editor.on("update", handleUpdate);

    // 清理函数
    return () => {
      resizeObserver.disconnect();
      mutationObserver.disconnect();
      editor.off("update", handleUpdate);
    };
  }, [editor, debouncedCheckScrollbar]);

  // 处理点击事件
  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      if (onClick) {
        onClick(e);
      }
    },
    [onClick]
  );

  // 处理鼠标按下事件
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (onMouseDown) {
        onMouseDown(e);
      }
    },
    [onMouseDown]
  );

  // 如果编辑器还没有初始化完成，显示加载状态
  if (!editor) {
    return (
      <div
        className={`basic-editor-loading ${className}`}
        style={style}
        title={title}
      >
        {placeholder}
      </div>
    );
  }

  return (
    <div
      className={`basic-editor-container ${className}`}
      onClick={handleClick}
      onMouseDown={handleMouseDown}
      style={{
        ...style,
        height: "100%",
        minHeight: "60px",
      }}
      title={title}
    >
      {/* 使用官方的 EditorContent 组件渲染编辑器 - 充满整个容器 */}
      <EditorContent
        editor={editor}
        className="basic-editor-content"
        style={{
          height: "100%",
          overflow: "auto",
        }}
        ref={editorContentRef}
      />
    </div>
  );
};

export default BasicEditor;
