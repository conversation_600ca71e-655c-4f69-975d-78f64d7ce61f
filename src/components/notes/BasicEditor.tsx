/**
 * 基础 Tiptap 编辑器组件
 * 严格按照官方文档实现，只包含最基本的功能
 * 参考：https://tiptap.dev/docs/editor/getting-started/install/react
 */

import React, { useCallback, useEffect } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import { Table } from "@tiptap/extension-table";
import { TableRow } from "@tiptap/extension-table-row";
import { TableCell } from "@tiptap/extension-table-cell";
import { TableHeader } from "@tiptap/extension-table-header";
import { TaskList } from "@tiptap/extension-list";
import { TaskItem } from "@tiptap/extension-list";
// BasicToolbar 现在由父组件管理

/**
 * 基础编辑器属性接口
 */
interface BasicEditorProps {
  /** 编辑器内容 */
  content?: string;
  /** 内容变化回调 */
  onChange?: (content: string) => void;
  /** 编辑器准备就绪回调 */
  onEditorReady?: (editor: any) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否自动聚焦 */
  autoFocus?: boolean;
  /** 是否可编辑 */
  editable?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 点击事件 */
  onClick?: (e: React.MouseEvent) => void;
  /** 鼠标按下事件 */
  onMouseDown?: (e: React.MouseEvent) => void;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 标题属性 */
  title?: string;
  // showToolbar 已移除，工具栏现在由父组件管理
}

/**
 * 基础编辑器组件
 * 使用 Tiptap 官方推荐的最简单配置
 */
const BasicEditor: React.FC<BasicEditorProps> = ({
  content = "",
  onChange,
  onEditorReady,
  placeholder = "开始输入...",
  autoFocus = false,
  editable = true,
  className = "",
  onClick,
  onMouseDown,
  style,
  title,
}) => {
  // 使用官方推荐的 useEditor hook
  const editor = useEditor({
    // 使用 StarterKit 扩展，包含最常用的基础功能
    extensions: [
      StarterKit.configure({
        // 可以在这里配置 StarterKit 的选项
        // 暂时使用默认配置
      }),
      // 添加 Placeholder 扩展，按照官方文档配置
      Placeholder.configure({
        placeholder: placeholder,
        emptyEditorClass: "is-editor-empty",
        emptyNodeClass: "is-empty",
        showOnlyWhenEditable: true,
        showOnlyCurrent: true,
      }),
      // 添加表格扩展，按照官方文档配置
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      // 添加任务列表扩展，按照官方文档配置
      TaskList.configure({
        HTMLAttributes: {
          class: "task-list",
        },
      }),
      // 添加任务项扩展，任务列表需要此扩展
      TaskItem.configure({
        HTMLAttributes: {
          class: "task-item",
        },
      }),
    ],
    // 设置初始内容
    content: content,
    // 设置是否可编辑
    editable: editable,
    // 设置是否自动聚焦
    autofocus: autoFocus,
    // 内容更新回调
    onUpdate: ({ editor }) => {
      if (onChange) {
        // 获取 HTML 格式的内容
        const html = editor.getHTML();
        onChange(html);
      }
    },
    // 编辑器创建完成回调
    onCreate: ({ editor }) => {
      if (onEditorReady) {
        onEditorReady(editor);
      }
    },
  });

  // 当外部 content 发生变化时，更新编辑器内容
  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content, { emitUpdate: false }); // 修正为对象类型
    }
  }, [content, editor]);

  // 当 editable 属性变化时，更新编辑器的可编辑状态
  useEffect(() => {
    if (editor) {
      editor.setEditable(editable);
    }
  }, [editable, editor]);

  // 处理点击事件
  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      if (onClick) {
        onClick(e);
      }
    },
    [onClick]
  );

  // 处理鼠标按下事件
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (onMouseDown) {
        onMouseDown(e);
      }
    },
    [onMouseDown]
  );

  // 如果编辑器还没有初始化完成，显示加载状态
  if (!editor) {
    return (
      <div
        className={`basic-editor-loading ${className}`}
        style={style}
        title={title}
      >
        {placeholder}
      </div>
    );
  }

  return (
    <div
      className={`basic-editor-container ${className}`}
      onClick={handleClick}
      onMouseDown={handleMouseDown}
      style={{
        ...style,
        height: "100%",
        minHeight: "60px",
      }}
      title={title}
    >
      {/* 使用官方的 EditorContent 组件渲染编辑器 - 充满整个容器 */}
      <EditorContent
        editor={editor}
        className="basic-editor-content"
        style={{
          height: "100%",
          overflow: "auto",
        }}
      />
    </div>
  );
};

export default BasicEditor;
